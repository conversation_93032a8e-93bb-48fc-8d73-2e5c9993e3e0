import { 
  storage, 
  serverStorage, 
  STORAGE_BUCKET_ID 
} from '../appwrite'
import { ID } from 'appwrite'
import { authService } from '../auth'

export interface UploadResult {
  fileId: string
  filename: string
  url: string
  size: number
  mimeType: string
}

export interface FileValidationOptions {
  maxSize?: number // in MB
  allowedTypes?: string[]
  allowedExtensions?: string[]
}

class AppwriteStorageService {
  private readonly defaultValidation: FileValidationOptions = {
    maxSize: 10, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
    allowedExtensions: ['jpg', 'jpeg', 'png', 'webp', 'gif']
  }

  // Validate file before upload
  validateFile(file: File, options?: FileValidationOptions): string | null {
    const validation = { ...this.defaultValidation, ...options }

    // Check file size
    if (validation.maxSize && file.size > validation.maxSize * 1024 * 1024) {
      return `File size must be less than ${validation.maxSize}MB`
    }

    // Check file type
    if (validation.allowedTypes && !validation.allowedTypes.includes(file.type)) {
      return `Invalid file type. Allowed types: ${validation.allowedTypes.join(', ')}`
    }

    // Check file extension
    if (validation.allowedExtensions) {
      const extension = file.name.split('.').pop()?.toLowerCase()
      if (!extension || !validation.allowedExtensions.includes(extension)) {
        return `Invalid file extension. Allowed extensions: ${validation.allowedExtensions.join(', ')}`
      }
    }

    return null
  }

  // Upload file to Appwrite Storage
  async uploadFile(
    file: File, 
    options?: FileValidationOptions & { 
      fileId?: string
      folder?: string 
    }
  ): Promise<UploadResult> {
    try {
      // Validate file
      const validationError = this.validateFile(file, options)
      if (validationError) {
        throw new Error(validationError)
      }

      // Check authentication
      const userId = await authService.getUserId()
      if (!userId) {
        throw new Error('Authentication required')
      }

      // Generate file ID with optional folder structure
      const timestamp = Date.now()
      const randomString = Math.random().toString(36).substring(2, 15)
      const extension = file.name.split('.').pop()
      const folder = options?.folder ? `${options.folder}/` : ''
      const fileId = options?.fileId || `${folder}${timestamp}-${randomString}.${extension}`

      // Upload to Appwrite Storage
      const uploadedFile = await storage.createFile(
        STORAGE_BUCKET_ID,
        fileId,
        file
      )

      // Get file URL
      const fileUrl = storage.getFileView(STORAGE_BUCKET_ID, uploadedFile.$id)

      return {
        fileId: uploadedFile.$id,
        filename: file.name,
        url: fileUrl.toString(),
        size: uploadedFile.sizeOriginal,
        mimeType: uploadedFile.mimeType
      }
    } catch (error: any) {
      throw new Error(`File upload failed: ${error.message}`)
    }
  }

  // Get file URL
  getFileUrl(fileId: string): string {
    const fileUrl = storage.getFileView(STORAGE_BUCKET_ID, fileId)
    return fileUrl.toString()
  }

  // Get file preview URL (for images)
  getFilePreview(
    fileId: string, 
    width?: number, 
    height?: number, 
    quality?: number
  ): string {
    const preview = storage.getFilePreview(
      STORAGE_BUCKET_ID,
      fileId,
      width,
      height,
      undefined, // gravity
      quality
    )
    return preview.toString()
  }

  // Delete file
  async deleteFile(fileId: string): Promise<void> {
    try {
      // Check authentication
      const userId = await authService.getUserId()
      if (!userId) {
        throw new Error('Authentication required')
      }

      await storage.deleteFile(STORAGE_BUCKET_ID, fileId)
    } catch (error: any) {
      throw new Error(`File deletion failed: ${error.message}`)
    }
  }

  // List files (admin only - using server storage)
  async listFiles(search?: string, limit?: number, offset?: number) {
    try {
      const files = await serverStorage.listFiles(
        STORAGE_BUCKET_ID,
        search ? [search] : undefined,
        limit,
        offset
      )
      
      return files.files.map(file => ({
        fileId: file.$id,
        name: file.name,
        size: file.sizeOriginal,
        mimeType: file.mimeType,
        createdAt: file.$createdAt,
        url: this.getFileUrl(file.$id)
      }))
    } catch (error: any) {
      throw new Error(`Failed to list files: ${error.message}`)
    }
  }

  // Upload multiple files
  async uploadMultipleFiles(
    files: File[], 
    options?: FileValidationOptions & { folder?: string }
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = []
    const errors: string[] = []

    for (const file of files) {
      try {
        const result = await this.uploadFile(file, options)
        results.push(result)
      } catch (error: any) {
        errors.push(`${file.name}: ${error.message}`)
      }
    }

    if (errors.length > 0 && results.length === 0) {
      throw new Error(`All uploads failed: ${errors.join(', ')}`)
    }

    return results
  }

  // Replace file (delete old, upload new)
  async replaceFile(
    oldFileId: string, 
    newFile: File, 
    options?: FileValidationOptions & { folder?: string }
  ): Promise<UploadResult> {
    try {
      // Upload new file first
      const uploadResult = await this.uploadFile(newFile, options)
      
      // Delete old file (don't fail if this fails)
      try {
        await this.deleteFile(oldFileId)
      } catch (error) {
        console.warn('Failed to delete old file:', error)
      }
      
      return uploadResult
    } catch (error: any) {
      throw new Error(`File replacement failed: ${error.message}`)
    }
  }

  // Get file metadata
  async getFileMetadata(fileId: string) {
    try {
      const file = await serverStorage.getFile(STORAGE_BUCKET_ID, fileId)
      return {
        fileId: file.$id,
        name: file.name,
        size: file.sizeOriginal,
        mimeType: file.mimeType,
        createdAt: file.$createdAt,
        updatedAt: file.$updatedAt,
        url: this.getFileUrl(file.$id)
      }
    } catch (error: any) {
      throw new Error(`Failed to get file metadata: ${error.message}`)
    }
  }
}

// Create and export service instance
export const appwriteStorageService = new AppwriteStorageService()
